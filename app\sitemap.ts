import { MetadataRoute } from "next";

export default function sitemap(): MetadataRoute.Sitemap {
	// Use environment variable for base URL, fallback to production domain
	const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || "https://granfondo-iles-de-guadeloupe.com";
	const lastModified = new Date();

	return [
		{
			url: baseUrl,
			lastModified,
			changeFrequency: "monthly",
			priority: 1,
		},
		// Note: Only include actual pages that exist
		// Hash fragments (#epreuve, #programme, etc.) should not be in sitemaps
		// Language pages (/en, /fr) should only be included if they exist as separate routes
	];
}
