# Deployment Checklist - Google Search Console Issues Fix

## 🚀 Pre-Deployment Checklist

### 1. Environment Variables
- [ ] Set `NEXT_PUBLIC_SITE_URL=https://granfondo-iles-de-guadeloupe.com` in production
- [ ] Verify environment variables are loaded correctly

### 2. Domain Configuration
- [ ] Ensure your hosting provider redirects HTTP to HTTPS
- [ ] Configure your hosting to redirect www to non-www (or vice versa)
- [ ] Verify SSL certificate is properly installed

### 3. DNS Configuration
- [ ] A record points to your server IP
- [ ] CNAME for www subdomain (if using www)
- [ ] Verify DNS propagation is complete

## 🔧 Post-Deployment Actions

### 1. Test Redirects
Test these URLs to ensure they redirect properly:
- [ ] `http://granfondo-iles-de-guadeloupe.com/` → `https://granfondo-iles-de-guadeloupe.com/`
- [ ] `http://www.granfondo-iles-de-guadeloupe.com/` → `https://granfondo-iles-de-guadeloupe.com/`
- [ ] `https://www.granfondo-iles-de-guadeloupe.com/` → `https://granfondo-iles-de-guadeloupe.com/`

### 2. Verify Technical Files
- [ ] Check `https://granfondo-iles-de-guadeloupe.com/robots.txt` is accessible
- [ ] Check `https://granfondo-iles-de-guadeloupe.com/sitemap.xml` is accessible
- [ ] Verify `https://granfondo-iles-de-guadeloupe.com/site.webmanifest` is accessible but not indexed

### 3. Google Search Console Actions
- [ ] Submit updated sitemap in Google Search Console
- [ ] Request indexing for the main page
- [ ] Monitor for "Page with redirect" errors (should decrease)
- [ ] Verify canonical URLs are working

### 4. Test SEO Elements
- [ ] Check meta tags in browser dev tools
- [ ] Test Open Graph with Facebook Debugger
- [ ] Test Twitter Cards with Twitter Card Validator
- [ ] Verify structured data with Google Rich Results Test

## 🐛 Troubleshooting Common Issues

### "Page with redirect" still showing
1. Wait 1-2 weeks for Google to re-crawl
2. Use "Request Indexing" in Google Search Console
3. Check that redirects are working at server level
4. Verify canonical URLs are correct

### "Crawled - currently not indexed" for technical files
1. This is normal and expected
2. Technical files (manifests, configs) don't need indexing
3. robots.txt now blocks these files from being crawled

### Sitemap errors
1. Verify sitemap only contains actual pages
2. Check all URLs use the correct domain
3. Ensure no hash fragments (#) in URLs
4. Resubmit sitemap after fixes

## 📊 Monitoring

### Weekly Checks
- [ ] Google Search Console for new errors
- [ ] Core Web Vitals performance
- [ ] Indexing status

### Monthly Checks
- [ ] Search rankings for target keywords
- [ ] Organic traffic trends
- [ ] Technical SEO audit

## 🎯 Expected Results

After deployment and Google re-crawling (1-2 weeks):
- ✅ "Page with redirect" errors should disappear
- ✅ Only canonical URLs should be indexed
- ✅ Technical files should not appear in indexing reports
- ✅ Improved search visibility

## 📞 Support

If issues persist after 2 weeks:
1. Check server-level redirects
2. Verify DNS configuration
3. Review hosting provider settings
4. Consider professional SEO audit
