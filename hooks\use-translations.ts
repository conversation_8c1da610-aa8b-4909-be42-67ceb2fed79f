"use client";

import enMessages from "../messages/en.json";
import frMessages from "../messages/fr.json";
import { useLanguage } from "./use-language";

type Messages = typeof frMessages;

export function useTranslations() {
	const { language } = useLanguage();

	const messages: Messages = language === "fr" ? frMessages : enMessages;

	const t = (key: string): string => {
		const keys = key.split(".");
		let value: any = messages;

		for (const k of keys) {
			if (value && typeof value === "object" && k in value) {
				value = value[k];
			} else {
				console.warn(`Translation key not found: ${key}`);
				return key;
			}
		}

		return typeof value === "string" ? value : key;
	};

	return { t, language };
}
