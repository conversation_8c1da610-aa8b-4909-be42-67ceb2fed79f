import { getRequestConfig } from 'next-intl/server';
import { headers } from 'next/headers';

export default getRequestConfig(async () => {
  // Get the locale from the pathname or use browser language detection
  const headersList = await headers();
  const acceptLanguage = headersList.get('accept-language') || '';
  
  // Simple language detection based on Accept-Language header
  const locale = acceptLanguage.startsWith('fr') ? 'fr' : 'en';

  return {
    locale,
    messages: (await import(`../messages/${locale}.json`)).default
  };
});
