# SEO Setup Guide - Gran Fondo Guadeloupe

## Google Search Console Setup

### 1. Create Google Search Console Account

1. Go to [Google Search Console](https://search.google.com/search-console/)
2. Sign in with your Google account
3. Click "Add Property"
4. Choose "URL prefix" and enter: `https://granfondo-iles-de-guadeloupe.com`

### 2. Verify Domain Ownership

You have several verification options:

#### Option A: HTML Meta Tag (Recommended)

1. Google will provide a meta tag like: `<meta name="google-site-verification" content="YOUR_VERIFICATION_CODE" />`
2. Replace `google-site-verification-code-here` in `app/layout.tsx` with your actual verification code
3. Deploy the changes
4. Click "Verify" in Google Search Console

#### Option B: HTML File Upload

1. Download the HTML verification file from Google
2. Upload it to the `public/` directory
3. Ensure it's accessible at `https://granfondo-iles-de-guadeloupe.com/google[verification-code].html`
4. Click "Verify" in Google Search Console

### 3. Submit Sitemap

1. Once verified, go to "Sitemaps" in the left sidebar
2. Add sitemap URL: `https://granfondo-iles-de-guadeloupe.com/sitemap.xml`
3. Click "Submit"

**Important Notes:**

-   The sitemap should only contain actual pages that exist on your domain
-   Do not include hash fragments (#epreuve, #programme, etc.) in sitemaps
-   Do not include language pages (/en, /fr) unless they exist as separate routes
-   All URLs in the sitemap must match your verified domain exactly

## Other Search Engine Verification

### Bing Webmaster Tools

1. Go to [Bing Webmaster Tools](https://www.bing.com/webmasters/)
2. Add your site: `https://granfondo-iles-de-guadeloupe.com`
3. Verify using meta tag method
4. Replace `yandex-verification-code-here` in `app/layout.tsx` with Bing's verification code

### Yandex Webmaster

1. Go to [Yandex Webmaster](https://webmaster.yandex.com/)
2. Add your site
3. Get verification meta tag
4. Replace `yandex-verification-code-here` in `app/layout.tsx`

## Favicon Files Needed

The following favicon files should be created and placed in the `public/` directory:

### Required Files:

-   `favicon.ico` (16x16, 32x32, 48x48 sizes in one file)
-   `favicon-16x16.png`
-   `favicon-32x32.png`
-   `favicon-96x96.png`
-   `favicon-192x192.png`
-   `favicon-512x512.png`
-   `apple-touch-icon.png` (180x180)
-   `safari-pinned-tab.svg` (monochrome SVG)
-   `mstile-70x70.png`
-   `mstile-150x150.png`
-   `mstile-310x150.png`
-   `mstile-310x310.png`

### Tools for Favicon Generation:

-   [RealFaviconGenerator](https://realfavicongenerator.net/)
-   [Favicon.io](https://favicon.io/)

## Social Media Images

### Required Images:

-   `og-image.svg` (1200x630) - Already created
-   `twitter-image.svg` (1200x600) - Already created

### Recommendations:

-   Convert SVG to JPG/PNG for better compatibility
-   Ensure text is readable at small sizes
-   Include brand colors and logo
-   Test on different social platforms

## Analytics Setup

### Google Analytics 4

1. Create GA4 property at [Google Analytics](https://analytics.google.com/)
2. Get Measurement ID (format: G-XXXXXXXXXX)
3. Add to Next.js using `@next/third-parties/google`

### Vercel Analytics

-   Already implemented with `@vercel/analytics`
-   No additional setup required

## Performance Monitoring

### Google PageSpeed Insights

-   Test regularly: [PageSpeed Insights](https://pagespeed.web.dev/)
-   Target scores: 90+ for all metrics

### Core Web Vitals

Monitor:

-   Largest Contentful Paint (LCP) < 2.5s
-   First Input Delay (FID) < 100ms
-   Cumulative Layout Shift (CLS) < 0.1

## SEO Checklist

### ✅ Completed:

-   [x] robots.txt file
-   [x] XML sitemap
-   [x] Meta tags (title, description)
-   [x] Open Graph metadata
-   [x] Twitter Card metadata
-   [x] Structured data (Event, Organization, Local Business)
-   [x] Favicon setup
-   [x] Language alternates (hreflang)
-   [x] Canonical URLs

### 🔄 Next Steps:

-   [ ] Verify Google Search Console
-   [ ] Generate and upload favicon files
-   [ ] Create optimized social media images
-   [ ] Set up Google Analytics
-   [ ] Monitor Core Web Vitals
-   [ ] Optimize heading hierarchy
-   [ ] Enhance image alt texts

## Maintenance

### Monthly Tasks:

-   Check Google Search Console for errors
-   Review Core Web Vitals performance
-   Update content for freshness
-   Monitor keyword rankings

### Quarterly Tasks:

-   Review and update structured data
-   Audit internal linking
-   Check for broken links
-   Update social media images if needed

## Contact Information

For SEO-related questions or issues, contact the development team or refer to this documentation.
